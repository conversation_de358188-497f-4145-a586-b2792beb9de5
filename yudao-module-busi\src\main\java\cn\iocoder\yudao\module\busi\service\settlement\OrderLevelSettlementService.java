package cn.iocoder.yudao.module.busi.service.settlement;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单级别交易结算服务
 * 支持追踪每笔转账对应的具体订单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderLevelSettlementService {

    /**
     * 执行订单级别的交易结算
     * @param tradeRecords 交易记录列表
     * @return 详细的结算结果，包含订单分配信息
     */
    public OrderSettlementResult settleWithOrderTracking(List<OrderTradeRecord> tradeRecords) {
        log.info("开始执行订单级别交易结算，交易记录数：{}", tradeRecords.size());

        // 预处理：将所有金额四舍五入到整数
        tradeRecords.forEach(record -> {
            BigDecimal roundedAmount = record.getAmount().setScale(0, RoundingMode.HALF_UP);
            record.setAmount(roundedAmount);
        });

        // 1. 计算每个用户的净余额
        Map<Long, BigDecimal> userBalances = calculateUserBalances(tradeRecords);
        log.info("用户净余额：{}", userBalances);

        // 2. 分离债权人和债务人
        Map<Long, BigDecimal> creditors = new HashMap<>();  // 应收款（正数）
        Map<Long, BigDecimal> debtors = new HashMap<>();    // 应付款（负数转正数）

        userBalances.forEach((user, balance) -> {
            if (balance.compareTo(BigDecimal.ZERO) > 0) {
                creditors.put(user, balance);
            } else if (balance.compareTo(BigDecimal.ZERO) < 0) {
                debtors.put(user, balance.abs());
            }
        });

        log.info("债权人：{}", creditors);
        log.info("债务人：{}", debtors);

        // 3. 构建未被抵消的订单债权
//        List<OrderCredit> activeCredits = buildActiveCredits(tradeRecords, userBalances);

        // 3. 构建净债权分配方案（而不是原始订单债权）
        List<OrderTransferRecord> transfers = allocateNetCredits(creditors, debtors, tradeRecords);

        // 4. 分配债务人向债权人的付款
//        List<OrderTransferRecord> transfers = allocatePayments(activeCredits, debtors);

        // 5. 构建结算结果
        OrderSettlementResult result = new OrderSettlementResult();
        result.setUserBalances(userBalances);
        result.setOrderTransfers(transfers);
        result.setTotalTransferCount(transfers.size());
        result.setTotalTransferAmount(transfers.stream()
                .map(OrderTransferRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        result.setSettlementReport(generateSettlementReport(transfers));

        log.info("订单级别结算完成，转账次数：{}，总转账金额：{}",
                result.getTotalTransferCount(), result.getTotalTransferAmount());

        return result;
    }

    /**
     * 基于净余额分配付款，并关联到具体订单
     */
    private List<OrderTransferRecord> allocateNetCredits(Map<Long, BigDecimal> creditors,
                                                         Map<Long, BigDecimal> debtors,
                                                         List<OrderTradeRecord> tradeRecords) {
//        List<OrderTransferRecord> transfers = new ArrayList<>();

        /// //////////////////////////// 简单的贪心匹配
//        // 为每个债权人构建其有效订单列表（用于分配说明）
//        Map<Long, List<OrderTradeRecord>> creditorOrders = tradeRecords.stream()
//                .filter(record -> creditors.containsKey(record.getSellerId()))
//                .collect(Collectors.groupingBy(OrderTradeRecord::getSellerId));
//
//        // 按金额排序优化分配
//        List<Map.Entry<Long, BigDecimal>> sortedCreditors = creditors.entrySet().stream()
//                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
//                .toList();
//
//        List<Map.Entry<Long, BigDecimal>> sortedDebtors = debtors.entrySet().stream()
//                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
//                .toList();
//
//        int i = 0, j = 0;
//        while (i < sortedCreditors.size() && j < sortedDebtors.size()) {
//            Map.Entry<Long, BigDecimal> creditorEntry = sortedCreditors.get(i);
//            Map.Entry<Long, BigDecimal> debtorEntry = sortedDebtors.get(j);
//
//            Long creditor = creditorEntry.getKey();
//            Long debtor = debtorEntry.getKey();
//            BigDecimal creditAmount = creditorEntry.getValue();
//            BigDecimal debtAmount = debtorEntry.getValue();
//
//            // 取较小值作为转账金额
//            BigDecimal transferAmount = creditAmount.min(debtAmount);
//
//            // 构建订单分配明细（按比例分配到具体订单）
//            List<OrderAllocation> allocations = buildOrderAllocations(
//                    creditor, transferAmount, creditorOrders.get(creditor));
//
//            transfers.add(new OrderTransferRecord(
//                    debtor, creditor, transferAmount, allocations, "订单结算"));
//
//            // 更新余额
//            creditorEntry.setValue(creditAmount.subtract(transferAmount));
//            debtorEntry.setValue(debtAmount.subtract(transferAmount));
//
//            // 移动指针
//            if (creditorEntry.getValue().compareTo(BigDecimal.ZERO) == 0) i++;
//            if (debtorEntry.getValue().compareTo(BigDecimal.ZERO) == 0) j++;
//        }
//
//        return transfers;



        /// /////////////////// 多层优化策略
        List<OrderTransferRecord> transfers = new ArrayList<>();
        // 为每个债权人构建其有效订单列表
        Map<Long, List<OrderTradeRecord>> creditorOrders = tradeRecords.stream()
                .filter(record -> creditors.containsKey(record.getSellerId()))
                .collect(Collectors.groupingBy(OrderTradeRecord::getSellerId));

        // 第一步：完全匹配抵消（相同金额直接抵消）
        performExactMatching(creditors, debtors, creditorOrders, transfers);

        // 第二步：大额优先匹配（减少大额拆分）
        performLargeAmountMatching(creditors, debtors, creditorOrders, transfers);

        // 第三步：剩余金额的最优匹配
        performOptimalMatching(creditors, debtors, creditorOrders, transfers);

        return transfers;
    }

    /**
     * 最小转账次数的最优匹配算法
     */
    private List<OrderTransferRecord> performMinimalTransferMatching(
            Map<Long, BigDecimal> creditors,
            Map<Long, BigDecimal> debtors,
            Map<Long, List<OrderTradeRecord>> creditorOrders) {
        
        List<OrderTransferRecord> transfers = new ArrayList<>();

        // 1. 转换为列表并排序（大额优先）
        List<CreditorNode> creditorList = creditors.entrySet().stream()
                .map(e -> new CreditorNode(e.getKey(), e.getValue()))
                .sorted((a, b) -> b.amount.compareTo(a.amount))
                .collect(Collectors.toList());

        List<DebtorNode> debtorList = debtors.entrySet().stream()
                .map(e -> new DebtorNode(e.getKey(), e.getValue()))
                .sorted((a, b) -> b.amount.compareTo(a.amount))
                .collect(Collectors.toList());

        // 2. 最优匹配：每个债务人尽可能匹配最少的债权人
        for (DebtorNode debtor : debtorList) {
            BigDecimal remainingDebt = debtor.amount;

            // 优先匹配能完全覆盖的债权人
            Iterator<CreditorNode> creditorIter = creditorList.iterator();
            while (creditorIter.hasNext() && remainingDebt.compareTo(BigDecimal.ZERO) > 0) {
                CreditorNode creditor = creditorIter.next();

                if (creditor.amount.compareTo(BigDecimal.ZERO) == 0) {
                    creditorIter.remove();
                    continue;
                }

                BigDecimal transferAmount = remainingDebt.min(creditor.amount);

                // 创建转账记录（暂不分配到具体订单）
                transfers.add(createTransferRecord(
                        debtor.userId, creditor.userId, transferAmount, creditorOrders));

                // 更新余额
                remainingDebt = remainingDebt.subtract(transferAmount);
                creditor.amount = creditor.amount.subtract(transferAmount);
            }
        }

        return transfers;
    }


    /**
     * 第一步：完全匹配抵消
     * 找到金额完全相等的债权债务进行直接抵消
     */
    private void performExactMatching(Map<Long, BigDecimal> creditors,
                                      Map<Long, BigDecimal> debtors,
                                      Map<Long, List<OrderTradeRecord>> creditorOrders,
                                      List<OrderTransferRecord> transfers) {

        List<Long> processedCreditors = new ArrayList<>();
        List<Long> processedDebtors = new ArrayList<>();

        for (Map.Entry<Long, BigDecimal> creditorEntry : creditors.entrySet()) {
            if (processedCreditors.contains(creditorEntry.getKey())) continue;

            BigDecimal creditAmount = creditorEntry.getValue();

            // 寻找完全匹配的债务人
            for (Map.Entry<Long, BigDecimal> debtorEntry : debtors.entrySet()) {
                if (processedDebtors.contains(debtorEntry.getKey())) continue;

                if (creditAmount.compareTo(debtorEntry.getValue()) == 0) {
                    // 完全匹配，直接抵消
                    Long creditor = creditorEntry.getKey();
                    Long debtor = debtorEntry.getKey();

                    List<OrderAllocation> allocations = buildOrderAllocations(
                            creditor, creditAmount, creditorOrders.get(creditor));

                    transfers.add(new OrderTransferRecord(
                            debtor, creditor, creditAmount, allocations, "完全匹配抵消"));

                    processedCreditors.add(creditor);
                    processedDebtors.add(debtor);

                    log.info("完全匹配抵消：{} -> {} : {} 元", debtor, creditor, creditAmount);
                    break;
                }
            }
        }

        // 移除已处理的项
        processedCreditors.forEach(creditors::remove);
        processedDebtors.forEach(debtors::remove);
    }

    /**
     * 第二步：大额优先匹配
     * 优先处理大额债权，避免被拆分成多笔小额转账
     */
    private void performLargeAmountMatching(Map<Long, BigDecimal> creditors,
                                            Map<Long, BigDecimal> debtors,
                                            Map<Long, List<OrderTradeRecord>> creditorOrders,
                                            List<OrderTransferRecord> transfers) {

        // 按金额降序排列债权人
        List<Map.Entry<Long, BigDecimal>> sortedCreditors = creditors.entrySet().stream()
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .toList();

        for (Map.Entry<Long, BigDecimal> creditorEntry : sortedCreditors) {
            if (creditorEntry.getValue().compareTo(BigDecimal.ZERO) == 0) continue;

            Long creditor = creditorEntry.getKey();
            BigDecimal creditAmount = creditorEntry.getValue();

            // 寻找能完全覆盖此债权的债务人
            Optional<Map.Entry<Long, BigDecimal>> matchingDebtor = debtors.entrySet().stream()
                    .filter(entry -> entry.getValue().compareTo(creditAmount) >= 0)
                    .min((a, b) -> a.getValue().compareTo(b.getValue())); // 选择最小的能覆盖的债务人

            if (matchingDebtor.isPresent()) {
                Long debtor = matchingDebtor.get().getKey();
                BigDecimal debtAmount = matchingDebtor.get().getValue();

                List<OrderAllocation> allocations = buildOrderAllocations(
                        creditor, creditAmount, creditorOrders.get(creditor));

                transfers.add(new OrderTransferRecord(
                        debtor, creditor, creditAmount, allocations, "大额优先匹配"));

                // 更新余额
                creditorEntry.setValue(BigDecimal.ZERO);
                debtors.put(debtor, debtAmount.subtract(creditAmount));

                log.info("大额优先匹配：{} -> {} : {} 元", debtor, creditor, creditAmount);
            }
        }

        // 清理已处理完的债权
        creditors.entrySet().removeIf(entry -> entry.getValue().compareTo(BigDecimal.ZERO) == 0);
        debtors.entrySet().removeIf(entry -> entry.getValue().compareTo(BigDecimal.ZERO) == 0);
    }

    /**
     * 第三步：剩余金额的最优匹配
     * 对剩余的债权债务进行传统的贪心匹配
     */
    private void performOptimalMatching(Map<Long, BigDecimal> creditors,
                                        Map<Long, BigDecimal> debtors,
                                        Map<Long, List<OrderTradeRecord>> creditorOrders,
                                        List<OrderTransferRecord> transfers) {

        List<Map.Entry<Long, BigDecimal>> sortedCreditors = creditors.entrySet().stream()
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .toList();

        List<Map.Entry<Long, BigDecimal>> sortedDebtors = debtors.entrySet().stream()
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .toList();

        int i = 0, j = 0;
        while (i < sortedCreditors.size() && j < sortedDebtors.size()) {
            Map.Entry<Long, BigDecimal> creditorEntry = sortedCreditors.get(i);
            Map.Entry<Long, BigDecimal> debtorEntry = sortedDebtors.get(j);

            Long creditor = creditorEntry.getKey();
            Long debtor = debtorEntry.getKey();
            BigDecimal creditAmount = creditorEntry.getValue();
            BigDecimal debtAmount = debtorEntry.getValue();

            // 取较小值作为转账金额
            BigDecimal transferAmount = creditAmount.min(debtAmount);

            // 构建订单分配明细
            List<OrderAllocation> allocations = buildOrderAllocations(
                    creditor, transferAmount, creditorOrders.get(creditor));

            transfers.add(new OrderTransferRecord(
                    debtor, creditor, transferAmount, allocations, "最优匹配"));

            // 更新余额
            creditorEntry.setValue(creditAmount.subtract(transferAmount));
            debtorEntry.setValue(debtAmount.subtract(transferAmount));

            // 移动指针
            if (creditorEntry.getValue().compareTo(BigDecimal.ZERO) == 0) i++;
            if (debtorEntry.getValue().compareTo(BigDecimal.ZERO) == 0) j++;

            log.info("最优匹配：{} -> {} : {} 元", debtor, creditor, transferAmount);
        }
    }

    /**
     * 按比例将转账金额分配到具体订单
     */
    private List<OrderAllocation> buildOrderAllocations(Long creditor, BigDecimal transferAmount,
                                                        List<OrderTradeRecord> orders) {
        if (orders == null || orders.isEmpty()) {
            return Collections.emptyList();
        }

        BigDecimal totalOrderAmount = orders.stream()
                .map(OrderTradeRecord::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        List<OrderAllocation> allocations = new ArrayList<>();
        BigDecimal allocated = BigDecimal.ZERO;

        for (int i = 0; i < orders.size(); i++) {
            OrderTradeRecord order = orders.get(i);
            BigDecimal allocationAmount;

            if (i == orders.size() - 1) {
                // 最后一个订单分配剩余金额，避免精度问题
                allocationAmount = transferAmount.subtract(allocated);
            } else {
                // 按比例分配，四舍五入到整数
                allocationAmount = transferAmount.multiply(order.getAmount())
                        .divide(totalOrderAmount, 0, RoundingMode.HALF_UP);
            }

            if (allocationAmount.compareTo(BigDecimal.ZERO) > 0) {
                allocations.add(new OrderAllocation(
                        order.getOrderId(), allocationAmount, order.getProductName()));
                allocated = allocated.add(allocationAmount);
            }
        }

        return allocations;
    }

    /**
     * 构建有效的订单债权（排除被抵消的订单）
     */
    private List<OrderCredit> buildActiveCredits(List<OrderTradeRecord> tradeRecords,
                                                 Map<Long, BigDecimal> userBalances) {
        List<OrderCredit> activeCredits = new ArrayList<>();

        for (OrderTradeRecord record : tradeRecords) {
            Long seller = record.getSellerId();

            // 只有净余额为正的卖家才有有效债权
            if (userBalances.getOrDefault(seller, BigDecimal.ZERO).compareTo(BigDecimal.ZERO) > 0) {
                activeCredits.add(new OrderCredit(
                        record.getOrderId(),
                        seller,              // 债权人（卖家）
                        record.getBuyerId(), // 原始债务人（买家）
                        record.getAmount(),
                        record.getProductName()
                ));
            }
        }

        log.info("有效订单债权：{}", activeCredits);
        return activeCredits;
    }


    /**
     * 分配债务人向债权人的付款
     */
    private List<OrderTransferRecord> allocatePayments(List<OrderCredit> activeCredits,
                                                       Map<Long, BigDecimal> debtors) {
        List<OrderTransferRecord> transfers = new ArrayList<>();

        // 为每个有效的订单债权分配付款人
        for (OrderCredit credit : activeCredits) {
            Long creditor = credit.getCreditorId(); // 债权人（应收款的人）
            BigDecimal creditAmount = credit.getAmount();

            log.info("处理订单{}的债权：{} 应收 {} 元", credit.getOrderId(), creditor, creditAmount);

            // 按债务金额排序，让欠款多的人优先支付
            List<Map.Entry<Long, BigDecimal>> sortedDebtors = debtors.entrySet().stream()
                    .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
                    .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                    .toList();

            BigDecimal remainingCredit = creditAmount;

            for (Map.Entry<Long, BigDecimal> debtorEntry : sortedDebtors) {
                if (remainingCredit.compareTo(BigDecimal.ZERO) <= 0) break;

                Long debtor = debtorEntry.getKey();
                BigDecimal debtorBalance = debtorEntry.getValue();

                // 计算这个债务人应该支付的金额
                BigDecimal paymentAmount = remainingCredit.min(debtorBalance);

                if (paymentAmount.compareTo(BigDecimal.ZERO) > 0) {
                    // 创建转账记录：债务人 -> 债权人
                    transfers.add(new OrderTransferRecord(
                            debtor,     // 付款人
                            creditor,   // 收款人
                            paymentAmount,
                            List.of(new OrderAllocation(credit.getOrderId(), paymentAmount, credit.getProductName())),
                            "订单结算"
                    ));

                    log.info("分配付款：{} 向 {} 支付 {} 元（订单{}）",
                            debtor, creditor, paymentAmount, credit.getOrderId());

                    // 更新余额
                    remainingCredit = remainingCredit.subtract(paymentAmount);
                    debtors.put(debtor, debtorBalance.subtract(paymentAmount));
                }
            }
        }

        return transfers;
    }

    /**
     * 计算用户净余额
     */
    private Map<Long, BigDecimal> calculateUserBalances(List<OrderTradeRecord> tradeRecords) {
        Map<Long, BigDecimal> balances = new HashMap<>();

        for (OrderTradeRecord record : tradeRecords) {
            // 卖家应收款增加
            balances.merge(record.getSellerId(), record.getAmount(), BigDecimal::add);
            // 买家应付款增加
            balances.merge(record.getBuyerId(), record.getAmount().negate(), BigDecimal::add);
        }

        return balances.entrySet().stream()
                .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        // 四舍五入到整数
                        entry -> entry.getValue().setScale(0, RoundingMode.HALF_UP),
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * 构建用户债务明细（用户欠别人的钱，按订单）
     */
    private Map<Long, List<OrderDebt>> buildUserDebts(List<OrderTradeRecord> tradeRecords) {
        Map<Long, List<OrderDebt>> userDebts = new HashMap<>();
        
        for (OrderTradeRecord record : tradeRecords) {
            userDebts.computeIfAbsent(record.getBuyerId(), k -> new ArrayList<>())
                    .add(new OrderDebt(record.getOrderId(), record.getSellerId(),
                                     record.getAmount(), record.getProductName()));
        }
        
        return userDebts;
    }

    /**
     * 构建用户债权明细（别人欠用户的钱，按订单）
     */
//    private Map<Long, List<OrderCredit>> buildUserCredits(List<OrderTradeRecord> tradeRecords) {
//        Map<Long, List<OrderCredit>> userCredits = new HashMap<>();
//
//        for (OrderTradeRecord record : tradeRecords) {
//            userCredits.computeIfAbsent(record.getSellerId(), k -> new ArrayList<>())
//                    .add(new OrderCredit(record.getOrderId(), record.getBuyerId(),
//                                       record.getAmount(), record.getProductName()));
//        }
//
//        return userCredits;
//    }

    /**
     * 计算订单级别的转账方案
     */
    private List<OrderTransferRecord> calculateOrderLevelTransfers(
            Map<Long, List<OrderDebt>> userDebts,
            Map<Long, List<OrderCredit>> userCredits) {

        List<OrderTransferRecord> transfers = new ArrayList<>();

        // 1. 先进行内部抵消，移除被完全抵消的订单
        performInternalOffset(userDebts, userCredits);

        // 2. 对剩余的债权进行分配
        for (Map.Entry<Long, List<OrderCredit>> entry : userCredits.entrySet()) {
            Long creditor = entry.getKey();
            List<OrderCredit> credits = entry.getValue();

            for (OrderCredit credit : credits) {
                if (credit.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                    // 这个订单还有未收回的款项，需要分配给债务人
                    allocateOrderDebt(credit, userDebts, transfers);
                }
            }
        }

        return transfers;
    }

    /**
     * 执行内部抵消，移除被完全抵消的订单
     */
    private void performInternalOffset(Map<Long, List<OrderDebt>> userDebts,
                                       Map<Long, List<OrderCredit>> userCredits) {

        // 遍历所有债权人
        for (Map.Entry<Long, List<OrderCredit>> creditorEntry : userCredits.entrySet()) {
            Long creditor = creditorEntry.getKey();
            List<OrderCredit> credits = creditorEntry.getValue();

            // 如果这个债权人同时也是债务人
            if (userDebts.containsKey(creditor)) {
                List<OrderDebt> debts = userDebts.get(creditor);

                // 用债权抵消债务
                for (OrderCredit credit : credits) {
                    for (OrderDebt debt : debts) {
                        if (debt.getCreditorId().equals(creditor)) continue; // 跳过自己欠自己的情况

                        BigDecimal offsetAmount = credit.getAmount().min(debt.getAmount());
                        if (offsetAmount.compareTo(BigDecimal.ZERO) > 0) {
                            credit.setAmount(credit.getAmount().subtract(offsetAmount));
                            debt.setAmount(debt.getAmount().subtract(offsetAmount));

                            log.info("内部抵消：{} 的债权订单{} 与债务 {} 抵消 {} 元",
                                    creditor, credit.getOrderId(), debt.getCreditorId(), offsetAmount);
                        }
                    }
                }
            }
        }

        // 移除已完全抵消的债权债务
        userCredits.values().forEach(credits ->
                credits.removeIf(credit -> credit.getAmount().compareTo(BigDecimal.ZERO) == 0));
        userDebts.values().forEach(debts ->
                debts.removeIf(debt -> debt.getAmount().compareTo(BigDecimal.ZERO) == 0));
    }

    /**
     * 为订单债权分配债务人
     */
    private void allocateOrderDebt(OrderCredit credit, Map<Long, List<OrderDebt>> userDebts,
                                   List<OrderTransferRecord> transfers) {

        BigDecimal remainingAmount = credit.getAmount();
        Long creditor = credit.getDebtorId(); // 注意：这里是原始的债务人，现在成为债权人

        // 按债务金额排序，优先让欠款多的人支付
        List<Map.Entry<Long, BigDecimal>> debtorBalances = userDebts.entrySet().stream()
                .map(entry -> {
                    BigDecimal totalDebt = entry.getValue().stream()
                            .map(OrderDebt::getAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return Map.entry(entry.getKey(), totalDebt);
                })
                .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
                .sorted((a, b) -> b.getValue().compareTo(a.getValue()))
                .toList();

        // 分配给债务人
        for (Map.Entry<Long, BigDecimal> debtorEntry : debtorBalances) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) break;

            Long debtor = debtorEntry.getKey();
            BigDecimal debtorBalance = debtorEntry.getValue();

            BigDecimal allocationAmount = remainingAmount.min(debtorBalance);

            if (allocationAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 创建转账记录
                transfers.add(new OrderTransferRecord(
                        debtor,
                        creditor,
                        allocationAmount,
                        List.of(new OrderAllocation(credit.getOrderId(), allocationAmount, credit.getProductName())),
                        "订单结算"
                ));

                remainingAmount = remainingAmount.subtract(allocationAmount);

                // 更新债务人的债务
                updateDebtorBalance(debtor, allocationAmount, userDebts);
            }
        }
    }

    /**
     * 更新债务人余额
     */
    private void updateDebtorBalance(Long debtor, BigDecimal paidAmount,
                                     Map<Long, List<OrderDebt>> userDebts) {
        List<OrderDebt> debts = userDebts.get(debtor);
        BigDecimal remaining = paidAmount;

        for (OrderDebt debt : debts) {
            if (remaining.compareTo(BigDecimal.ZERO) <= 0) break;

            BigDecimal deduction = remaining.min(debt.getAmount());
            debt.setAmount(debt.getAmount().subtract(deduction));
            remaining = remaining.subtract(deduction);
        }
    }

    /**
     * 生成结算报告
     */
    private String generateSettlementReport(List<OrderTransferRecord> transfers) {
        StringBuilder report = new StringBuilder();
        report.append("=== 订单级别结算报告 ===\n");
        
        for (OrderTransferRecord transfer : transfers) {
            report.append(String.format("%s 向 %s 转账 %s 元 (%s)\n",
                    transfer.getFromId(), transfer.getToId(), transfer.getAmount(), transfer.getRemark()));
            
            for (OrderAllocation allocation : transfer.getOrderAllocations()) {
                report.append(String.format("  - 订单 %d: %s 元 (%s)\n",
                        allocation.getOrderId(), allocation.getAmount(), allocation.getProductName()));
            }
        }
        
        return report.toString();
    }

    // ========== 内部类定义 ==========

    /**
     * 订单交易记录
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class OrderTradeRecord {
        private Long orderId;       // 订单ID
//        private String seller;      // 卖家
//        private String buyer;       // 买家
        private Long sellerId;      // 卖家id
        private Long buyerId;       // 买家id
        private BigDecimal amount;  // 金额
        private String productName; // 商品名称
    }

    /**
     * 订单债务
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class OrderDebt {
        private Long orderId;       // 订单ID
//        private String creditor;    // 债权人
        private Long creditorId;    // 债权人
        private BigDecimal amount;  // 债务金额
        private String productName; // 商品名称
    }

    /**
     * 订单债权
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class OrderCredit {
        private Long orderId;       // 订单ID
        private Long creditorId;    // 债权人ID（卖家）
        private Long debtorId;      // 债务人ID（买家）
        private BigDecimal amount;  // 债权金额
        private String productName; // 商品名称
    }

    /**
     * 订单分配
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class OrderAllocation {
        private Long orderId;       // 订单ID
        private BigDecimal amount;  // 分配金额
        private String productName; // 商品名称
    }

    /**
     * 订单转账记录
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class OrderTransferRecord {
//        private String from;                        // 付款人
//        private String to;                          // 收款人
        private Long fromId;                        // 付款人
        private Long toId;                          // 收款人
        private BigDecimal amount;                  // 转账金额
        private List<OrderAllocation> orderAllocations; // 订单分配明细
        private String remark;                      // 备注
    }

    /**
     * 订单结算结果
     */
    @lombok.Data
    public static class OrderSettlementResult {
        private Map<Long, BigDecimal> userBalances;       // 用户净余额
        private List<OrderTransferRecord> orderTransfers;   // 订单转账方案
        private Integer totalTransferCount;                 // 总转账次数
        private BigDecimal totalTransferAmount;             // 总转账金额
        private String settlementReport;                    // 结算报告
    }
}
